"""
api_call_tool.py

HTTP API call tool for making requests to external services.

Author: <PERSON>
"""
import asyncio
import json
import logging
import random
from typing import Dict, Any, Optional, List, Tuple
from urllib.parse import urlparse

import httpx

from .base_tool import BaseTool, tool_method

logger = logging.getLogger(__name__)


class ApiCallTool(BaseTool):
    """Tool for making HTTP API calls with safety constraints, retry logic, and comprehensive response handling."""

    def __init__(self):
        super().__init__()
        self._client = None

    @property
    def tool_name(self) -> str:
        return "api_call_tool"

    @property
    def tool_description(self) -> str:
        return "Make HTTP API calls to external services with safety constraints, automatic retry logic, and comprehensive response handling"

    def _get_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client with default configuration."""
        if self._client is None:
            self._client = httpx.AsyncClient(
                timeout=httpx.Timeout(30.0),  # 30 second timeout
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5),
                follow_redirects=True
            )
        return self._client

    def _validate_url(self, url: str) -> Dict[str, Any]:
        """
        Validate URL for safety and format.
        
        Args:
            url: URL to validate
            
        Returns:
            Dictionary with validation result
        """
        try:
            parsed = urlparse(url)
            
            # Check scheme
            if parsed.scheme not in ['http', 'https']:
                return {
                    "valid": False,
                    "error": "URL must use HTTP or HTTPS protocol"
                }
            
            # Check if hostname exists
            if not parsed.netloc:
                return {
                    "valid": False,
                    "error": "URL must have a valid hostname"
                }
            
            # Block localhost and private IPs for security
            hostname = parsed.hostname
            if hostname:
                hostname_lower = hostname.lower()
                blocked_hosts = [
                    'localhost', '127.0.0.1', '0.0.0.0',
                    '::1', '169.254.', '10.', '172.16.', '192.168.'
                ]
                
                for blocked in blocked_hosts:
                    if hostname_lower.startswith(blocked) or blocked in hostname_lower:
                        return {
                            "valid": False,
                            "error": f"Access to {hostname} is not allowed for security reasons"
                        }
            
            return {
                "valid": True,
                "parsed": parsed
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Invalid URL format: {str(e)}"
            }

    def _prepare_headers(self, headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Prepare headers with defaults and validation.
        
        Args:
            headers: Optional custom headers
            
        Returns:
            Prepared headers dictionary
        """
        default_headers = {
            "User-Agent": "Orchestra-Template-Engine/1.0",
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate"
        }
        
        if headers:
            # Validate and merge headers
            for key, value in headers.items():
                if isinstance(key, str) and isinstance(value, str):
                    default_headers[key] = value
        
        return default_headers

    def _format_response(self, response: httpx.Response, include_body: bool = True) -> Dict[str, Any]:
        """
        Format HTTP response into a structured dictionary.
        
        Args:
            response: httpx Response object
            include_body: Whether to include response body
            
        Returns:
            Formatted response dictionary
        """
        result = {
            "status_code": response.status_code,
            "status_text": response.reason_phrase,
            "headers": dict(response.headers),
            "url": str(response.url),
            "elapsed_ms": int(response.elapsed.total_seconds() * 1000),
            "success": 200 <= response.status_code < 300
        }
        
        if include_body:
            try:
                # Try to parse as JSON first
                content_type = response.headers.get("content-type", "").lower()
                
                if "application/json" in content_type:
                    try:
                        result["body"] = response.json()
                        result["body_type"] = "json"
                    except json.JSONDecodeError:
                        result["body"] = response.text
                        result["body_type"] = "text"
                else:
                    # Handle text content
                    result["body"] = response.text
                    result["body_type"] = "text"
                    
                result["body_size"] = len(response.content)
                
            except Exception as e:
                result["body"] = f"Error reading response body: {str(e)}"
                result["body_type"] = "error"
                result["body_size"] = 0
        
        return result

    def _should_retry(self, response: Optional[httpx.Response], exception: Optional[Exception]) -> bool:
        """
        Determine if a request should be retried based on response or exception.

        Args:
            response: HTTP response (if request succeeded)
            exception: Exception that occurred (if request failed)

        Returns:
            True if the request should be retried
        """
        # Retry on network/connection errors
        if exception:
            if isinstance(exception, (httpx.TimeoutException, httpx.ConnectError, httpx.ReadError)):
                return True
            # Don't retry on other exceptions (like URL validation errors)
            return False

        # Retry on specific HTTP status codes
        if response:
            # Retry on server errors (5xx) and some client errors
            retry_status_codes = {
                429,  # Too Many Requests (rate limiting)
                500,  # Internal Server Error
                502,  # Bad Gateway
                503,  # Service Unavailable
                504,  # Gateway Timeout
                507,  # Insufficient Storage
                508,  # Loop Detected
                510,  # Not Extended
                511,  # Network Authentication Required
            }
            return response.status_code in retry_status_codes

        return False

    def _calculate_backoff_delay(self, attempt: int, base_delay: float = 1.0, max_delay: float = 60.0) -> float:
        """
        Calculate exponential backoff delay with jitter.

        Args:
            attempt: Current attempt number (0-based)
            base_delay: Base delay in seconds
            max_delay: Maximum delay in seconds

        Returns:
            Delay in seconds
        """
        # Exponential backoff: base_delay * (2 ^ attempt)
        delay = base_delay * (2 ** attempt)

        # Cap at max_delay
        delay = min(delay, max_delay)

        # Add jitter (±25% of delay)
        jitter = delay * 0.25 * (2 * random.random() - 1)
        delay += jitter

        # Ensure delay is positive
        return max(0.1, delay)

    async def _make_request_with_retry(
        self,
        method: str,
        url: str,
        max_retries: int = 3,
        base_delay: float = 1.0,
        timeout: int = 30,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Make an HTTP request with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: Target URL
            max_retries: Maximum number of retry attempts
            base_delay: Base delay for exponential backoff
            timeout: Request timeout in seconds
            **kwargs: Additional arguments for the HTTP request

        Returns:
            Dictionary with response data and metadata
        """
        client = self._get_client()
        last_exception = None
        last_response = None

        for attempt in range(max_retries + 1):  # +1 for initial attempt
            try:
                logger.info(f"Making {method} request to {url} (attempt {attempt + 1}/{max_retries + 1})")

                # Make the request
                response = await client.request(
                    method=method,
                    url=url,
                    timeout=timeout,
                    **kwargs
                )

                last_response = response

                # Check if we should retry based on status code
                if self._should_retry(response, None):
                    if attempt < max_retries:
                        delay = self._calculate_backoff_delay(attempt, base_delay)
                        logger.warning(
                            f"{method} request to {url} returned {response.status_code}, "
                            f"retrying in {delay:.2f}s (attempt {attempt + 1}/{max_retries + 1})"
                        )
                        await asyncio.sleep(delay)
                        continue
                    else:
                        logger.error(
                            f"{method} request to {url} failed after {max_retries + 1} attempts "
                            f"(final status: {response.status_code})"
                        )

                # Success or final attempt - format and return response
                result = self._format_response(response)
                if attempt > 0:
                    result["retry_info"] = {
                        "attempts": attempt + 1,
                        "max_retries": max_retries,
                        "succeeded_on_retry": True
                    }

                logger.info(f"{method} request completed: {response.status_code} in {result['elapsed_ms']}ms")
                return result

            except Exception as e:
                last_exception = e

                # Check if we should retry based on exception
                if self._should_retry(None, e):
                    if attempt < max_retries:
                        delay = self._calculate_backoff_delay(attempt, base_delay)
                        logger.warning(
                            f"{method} request to {url} failed with {type(e).__name__}: {str(e)}, "
                            f"retrying in {delay:.2f}s (attempt {attempt + 1}/{max_retries + 1})"
                        )
                        await asyncio.sleep(delay)
                        continue
                    else:
                        logger.error(
                            f"{method} request to {url} failed after {max_retries + 1} attempts "
                            f"(final error: {type(e).__name__}: {str(e)})"
                        )

                # Don't retry this exception or max retries reached
                break

        # All retries exhausted - return error response
        if last_exception:
            error_response = {
                "success": False,
                "status_code": 0,
                "retry_info": {
                    "attempts": max_retries + 1,
                    "max_retries": max_retries,
                    "succeeded_on_retry": False
                }
            }

            if isinstance(last_exception, httpx.TimeoutException):
                error_response["error"] = f"Request timed out after {timeout} seconds (tried {max_retries + 1} times)"
            elif isinstance(last_exception, httpx.RequestError):
                error_response["error"] = f"Request failed: {str(last_exception)} (tried {max_retries + 1} times)"
            else:
                error_response["error"] = f"Unexpected error: {str(last_exception)} (tried {max_retries + 1} times)"

            return error_response

        elif last_response:
            # Final response was not successful
            result = self._format_response(last_response)
            result["retry_info"] = {
                "attempts": max_retries + 1,
                "max_retries": max_retries,
                "succeeded_on_retry": False
            }
            return result

        # Should not reach here
        return {
            "success": False,
            "error": "Unknown error occurred during retry logic",
            "status_code": 0
        }

    @tool_method(name="get")
    async def make_get_request(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, str]] = None,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Dict[str, Any]:
        """
        Make a GET request to the specified URL with retry logic.

        Args:
            url: Target URL for the request
            headers: Optional HTTP headers
            params: Optional query parameters
            timeout: Request timeout in seconds (default: 30)
            max_retries: Maximum number of retry attempts (default: 3)
            retry_delay: Base delay for exponential backoff in seconds (default: 1.0)

        Returns:
            Dictionary with response data and metadata
        """
        # Validate URL
        url_validation = self._validate_url(url)
        if not url_validation["valid"]:
            return {
                "success": False,
                "error": url_validation["error"],
                "status_code": 0
            }

        prepared_headers = self._prepare_headers(headers)

        return await self._make_request_with_retry(
            method="GET",
            url=url,
            max_retries=max_retries,
            base_delay=retry_delay,
            timeout=timeout,
            headers=prepared_headers,
            params=params
        )

    @tool_method(name="post")
    async def make_post_request(
        self,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Dict[str, Any]:
        """
        Make a POST request to the specified URL with retry logic.

        Args:
            url: Target URL for the request
            data: Optional form data (will be form-encoded)
            json_data: Optional JSON data (will be JSON-encoded)
            headers: Optional HTTP headers
            timeout: Request timeout in seconds (default: 30)
            max_retries: Maximum number of retry attempts (default: 3)
            retry_delay: Base delay for exponential backoff in seconds (default: 1.0)

        Returns:
            Dictionary with response data and metadata
        """
        # Validate URL
        url_validation = self._validate_url(url)
        if not url_validation["valid"]:
            return {
                "success": False,
                "error": url_validation["error"],
                "status_code": 0
            }

        # Validate that only one data type is provided
        if data is not None and json_data is not None:
            return {
                "success": False,
                "error": "Cannot provide both 'data' and 'json_data'. Choose one.",
                "status_code": 0
            }

        prepared_headers = self._prepare_headers(headers)

        # Prepare request kwargs based on data type
        request_kwargs = {}
        if json_data is not None:
            request_kwargs["json"] = json_data
        else:
            request_kwargs["data"] = data

        return await self._make_request_with_retry(
            method="POST",
            url=url,
            max_retries=max_retries,
            base_delay=retry_delay,
            timeout=timeout,
            headers=prepared_headers,
            **request_kwargs
        )

    @tool_method(name="put")
    async def make_put_request(
        self,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Dict[str, Any]:
        """
        Make a PUT request to the specified URL with retry logic.

        Args:
            url: Target URL for the request
            data: Optional form data (will be form-encoded)
            json_data: Optional JSON data (will be JSON-encoded)
            headers: Optional HTTP headers
            timeout: Request timeout in seconds (default: 30)
            max_retries: Maximum number of retry attempts (default: 3)
            retry_delay: Base delay for exponential backoff in seconds (default: 1.0)

        Returns:
            Dictionary with response data and metadata
        """
        # Validate URL
        url_validation = self._validate_url(url)
        if not url_validation["valid"]:
            return {
                "success": False,
                "error": url_validation["error"],
                "status_code": 0
            }

        # Validate that only one data type is provided
        if data is not None and json_data is not None:
            return {
                "success": False,
                "error": "Cannot provide both 'data' and 'json_data'. Choose one.",
                "status_code": 0
            }

        prepared_headers = self._prepare_headers(headers)

        # Prepare request kwargs based on data type
        request_kwargs = {}
        if json_data is not None:
            request_kwargs["json"] = json_data
        else:
            request_kwargs["data"] = data

        return await self._make_request_with_retry(
            method="PUT",
            url=url,
            max_retries=max_retries,
            base_delay=retry_delay,
            timeout=timeout,
            headers=prepared_headers,
            **request_kwargs
        )

    @tool_method(name="delete")
    async def make_delete_request(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Dict[str, Any]:
        """
        Make a DELETE request to the specified URL with retry logic.

        Args:
            url: Target URL for the request
            headers: Optional HTTP headers
            timeout: Request timeout in seconds (default: 30)
            max_retries: Maximum number of retry attempts (default: 3)
            retry_delay: Base delay for exponential backoff in seconds (default: 1.0)

        Returns:
            Dictionary with response data and metadata
        """
        # Validate URL
        url_validation = self._validate_url(url)
        if not url_validation["valid"]:
            return {
                "success": False,
                "error": url_validation["error"],
                "status_code": 0
            }

        prepared_headers = self._prepare_headers(headers)

        return await self._make_request_with_retry(
            method="DELETE",
            url=url,
            max_retries=max_retries,
            base_delay=retry_delay,
            timeout=timeout,
            headers=prepared_headers
        )

    @tool_method(name="head")
    async def make_head_request(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Dict[str, Any]:
        """
        Make a HEAD request to the specified URL (headers only, no body) with retry logic.

        Args:
            url: Target URL for the request
            headers: Optional HTTP headers
            timeout: Request timeout in seconds (default: 30)
            max_retries: Maximum number of retry attempts (default: 3)
            retry_delay: Base delay for exponential backoff in seconds (default: 1.0)

        Returns:
            Dictionary with response metadata (no body)
        """
        # Validate URL
        url_validation = self._validate_url(url)
        if not url_validation["valid"]:
            return {
                "success": False,
                "error": url_validation["error"],
                "status_code": 0
            }

        prepared_headers = self._prepare_headers(headers)

        # Use the retry mechanism but override response formatting for HEAD requests
        result = await self._make_request_with_retry(
            method="HEAD",
            url=url,
            max_retries=max_retries,
            base_delay=retry_delay,
            timeout=timeout,
            headers=prepared_headers
        )

        # For HEAD requests, remove body information if present
        if "body" in result:
            del result["body"]
        if "body_type" in result:
            del result["body_type"]
        if "body_size" in result:
            del result["body_size"]

        return result

    @tool_method(name="test_connectivity")
    async def test_connectivity(
        self,
        url: str,
        timeout: int = 10,
        max_retries: int = 2,
        retry_delay: float = 0.5
    ) -> Dict[str, Any]:
        """
        Test connectivity to a URL with minimal overhead and retry logic.

        Args:
            url: Target URL to test
            timeout: Request timeout in seconds (default: 10)
            max_retries: Maximum number of retry attempts (default: 2, lower for connectivity tests)
            retry_delay: Base delay for exponential backoff in seconds (default: 0.5, faster for connectivity)

        Returns:
            Dictionary with connectivity test results
        """
        # Validate URL
        url_validation = self._validate_url(url)
        if not url_validation["valid"]:
            return {
                "success": False,
                "error": url_validation["error"],
                "reachable": False
            }

        # Use the retry mechanism for connectivity testing
        result = await self._make_request_with_retry(
            method="HEAD",
            url=url,
            max_retries=max_retries,
            base_delay=retry_delay,
            timeout=timeout,
            follow_redirects=True
        )

        # Transform the result for connectivity testing format
        if result.get("success", False):
            return {
                "success": True,
                "reachable": True,
                "status_code": result.get("status_code"),
                "elapsed_ms": result.get("elapsed_ms"),
                "final_url": result.get("url"),
                "server": result.get("headers", {}).get("server", "Unknown"),
                "retry_info": result.get("retry_info")
            }
        else:
            return {
                "success": False,
                "reachable": False,
                "error": result.get("error", "Unknown error"),
                "retry_info": result.get("retry_info")
            }

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit - cleanup client."""
        if self._client:
            await self._client.aclose()
            self._client = None
