"""
jira_tool.py

Jira integration tool for reading tickets and performing Jira operations.

Author: <PERSON>
"""

import asyncio
import base64
import json
from typing import Any, Dict, List, Optional
from urllib.parse import quote

import aiohttp

from .base_tool import BaseTool, tool_method
from dotenv import load_dotenv

load_dotenv()


class JiraTool(BaseTool):
    """Tool for interacting with Jira instances."""

    def __init__(self):
        super().__init__()
        # Create fresh settings instance to ensure .env file is loaded
        from settings import Settings
        self.settings = Settings()
        self.jira_config = self.settings.jira

    @property
    def tool_name(self) -> str:
        return "jira_tool"

    @property
    def tool_description(self) -> str:
        return "Tool for reading Jira tickets and performing Jira operations"

    def _ensure_config_loaded(self):
        """Ensure Jira configuration is properly loaded."""
        if not self.jira_config.base_url:
            # Try to reload settings if configuration is missing
            from settings import Settings
            self.settings = Settings()
            self.jira_config = self.settings.jira

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Jira API requests."""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        if self.jira_config.auth_type == "token":
            # API Token authentication (recommended for Jira Cloud)
            auth_string = f"{self.jira_config.username}:{self.jira_config.api_token}"
            encoded_auth = base64.b64encode(auth_string.encode()).decode()
            headers["Authorization"] = f"Basic {encoded_auth}"
        elif self.jira_config.auth_type == "basic":
            # Basic authentication
            auth_string = f"{self.jira_config.username}:{self.jira_config.api_token}"
            encoded_auth = base64.b64encode(auth_string.encode()).decode()
            headers["Authorization"] = f"Basic {encoded_auth}"

        return headers

    def _build_url(self, endpoint: str) -> str:
        """Build full URL for Jira API endpoint."""
        if not self.jira_config.base_url:
            raise ValueError("Jira base URL not configured")
        
        # Ensure base URL ends with /
        base_url = self.jira_config.base_url.rstrip('/')
        # Ensure endpoint starts with /
        endpoint = endpoint.lstrip('/')
        
        return f"{base_url}/rest/api/2/{endpoint}"

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make an authenticated request to Jira API with retry logic."""
        url = self._build_url(endpoint)
        headers = self._get_auth_headers()

        for attempt in range(self.jira_config.max_retries + 1):
            try:
                timeout = aiohttp.ClientTimeout(total=self.jira_config.timeout)
                connector = aiohttp.TCPConnector(verify_ssl=self.jira_config.verify_ssl)
                
                async with aiohttp.ClientSession(
                    timeout=timeout,
                    connector=connector
                ) as session:
                    async with session.request(
                        method=method,
                        url=url,
                        headers=headers,
                        params=params,
                        json=data
                    ) as response:
                        response_data = await response.text()
                        
                        if 200 <= response.status < 300:  # Accept all 2xx status codes
                            try:
                                return {
                                    "success": True,
                                    "data": json.loads(response_data),
                                    "status_code": response.status
                                }
                            except json.JSONDecodeError:
                                return {
                                    "success": True,
                                    "data": response_data,
                                    "status_code": response.status
                                }
                        elif response.status == 401:
                            return {
                                "success": False,
                                "error": "Authentication failed. Check your credentials.",
                                "status_code": response.status
                            }
                        elif response.status == 403:
                            return {
                                "success": False,
                                "error": "Access forbidden. Check your permissions.",
                                "status_code": response.status
                            }
                        elif response.status == 404:
                            return {
                                "success": False,
                                "error": "Resource not found.",
                                "status_code": response.status
                            }
                        else:
                            # For other status codes, try to parse error message
                            try:
                                error_data = json.loads(response_data)
                                error_msg = error_data.get("errorMessages", [])
                                if error_msg:
                                    error_text = "; ".join(error_msg)
                                else:
                                    error_text = error_data.get("message", response_data)
                            except json.JSONDecodeError:
                                error_text = response_data

                            if attempt < self.jira_config.max_retries:
                                # Retry for server errors (5xx)
                                if 500 <= response.status < 600:
                                    await asyncio.sleep(
                                        self.jira_config.retry_delay * (2 ** attempt)
                                    )
                                    continue
                            
                            return {
                                "success": False,
                                "error": f"Request failed: {error_text}",
                                "status_code": response.status
                            }

            except aiohttp.ClientError as e:
                if attempt < self.jira_config.max_retries:
                    await asyncio.sleep(self.jira_config.retry_delay * (2 ** attempt))
                    continue
                
                return {
                    "success": False,
                    "error": f"Network error: {str(e)}",
                    "status_code": 0
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": f"Unexpected error: {str(e)}",
                    "status_code": 0
                }

        return {
            "success": False,
            "error": f"Request failed after {self.jira_config.max_retries} retries",
            "status_code": 0
        }

    @tool_method(name="test_connection")
    async def test_connection(self) -> Dict[str, Any]:
        """
        Test connection to Jira instance.

        Returns:
            Dictionary with connection test results
        """
        # Ensure configuration is loaded
        self._ensure_config_loaded()

        if not self.jira_config.base_url:
            return {
                "success": False,
                "error": "Jira base URL not configured",
                "reachable": False
            }

        if not self.jira_config.username or not self.jira_config.api_token:
            return {
                "success": False,
                "error": "Jira credentials not configured",
                "reachable": False
            }

        try:
            result = await self._make_request("GET", "myself")
            
            if result["success"]:
                user_data = result["data"]
                return {
                    "success": True,
                    "message": "Successfully connected to Jira",
                    "reachable": True,
                    "user": {
                        "name": user_data.get("displayName"),
                        "email": user_data.get("emailAddress"),
                        "account_id": user_data.get("accountId")
                    },
                    "jira_url": self.jira_config.base_url
                }
            else:
                return {
                    "success": False,
                    "error": result["error"],
                    "reachable": False
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Connection test failed: {str(e)}",
                "reachable": False
            }

    @tool_method(name="get_ticket")
    async def get_ticket(
        self,
        ticket_key: str,
        expand: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get a Jira ticket by its key or ID.

        Args:
            ticket_key: Jira ticket key (e.g., 'PROJ-123') or ID
            expand: Optional comma-separated list of fields to expand
                   (e.g., 'changelog,renderedFields,names,schema,operations')

        Returns:
            Dictionary with ticket data or error information
        """
        if not ticket_key:
            return {
                "success": False,
                "error": "Ticket key is required"
            }

        params = {}
        if expand:
            params["expand"] = expand

        endpoint = f"issue/{quote(ticket_key)}"
        result = await self._make_request("GET", endpoint, params=params)

        if result["success"]:
            ticket_data = result["data"]
            
            # Extract key information for easier access
            fields = ticket_data.get("fields", {})
            
            return {
                "success": True,
                "ticket": {
                    "key": ticket_data.get("key"),
                    "id": ticket_data.get("id"),
                    "summary": fields.get("summary"),
                    "description": fields.get("description"),
                    "status": fields.get("status", {}).get("name"),
                    "priority": fields.get("priority", {}).get("name"),
                    "assignee": fields.get("assignee", {}).get("displayName") if fields.get("assignee") else None,
                    "reporter": fields.get("reporter", {}).get("displayName") if fields.get("reporter") else None,
                    "created": fields.get("created"),
                    "updated": fields.get("updated"),
                    "issue_type": fields.get("issuetype", {}).get("name"),
                    "project": fields.get("project", {}).get("key"),
                    "labels": fields.get("labels", []),
                    "components": [comp.get("name") for comp in fields.get("components", [])],
                    "fix_versions": [ver.get("name") for ver in fields.get("fixVersions", [])],
                    "url": f"{self.jira_config.base_url}/browse/{ticket_data.get('key')}"
                },
                "raw_data": ticket_data
            }
        else:
            return result

    @tool_method(name="create_ticket")
    async def create_ticket(
        self,
        project_key: str,
        summary: str,
        issue_type: str,
        description: Optional[str] = None,
        priority: Optional[str] = None,
        assignee: Optional[str] = None,
        reporter: Optional[str] = None,
        labels: Optional[List[str]] = None,
        components: Optional[List[str]] = None,
        fix_versions: Optional[List[str]] = None,
        custom_fields: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a new Jira ticket.

        Args:
            project_key: Project key (e.g., 'PROJ')
            summary: Ticket summary/title
            issue_type: Issue type (e.g., 'Bug', 'Task', 'Story')
            description: Optional ticket description
            priority: Optional priority (e.g., 'High', 'Medium', 'Low')
            assignee: Optional assignee username, email, or account ID
            reporter: Optional reporter username, email, or account ID
            labels: Optional list of labels
            components: Optional list of component names
            fix_versions: Optional list of fix version names
            custom_fields: Optional dictionary of custom field values

        Returns:
            Dictionary with created ticket information or error details
        """
        # Ensure configuration is loaded
        self._ensure_config_loaded()

        if not project_key:
            return {
                "success": False,
                "error": "Project key is required"
            }

        if not summary:
            return {
                "success": False,
                "error": "Summary is required"
            }

        if not issue_type:
            return {
                "success": False,
                "error": "Issue type is required"
            }

        # Build the ticket data
        fields = {
            "project": {"key": project_key},
            "summary": summary,
            "issuetype": {"name": issue_type}
        }

        # Add optional fields
        if description:
            fields["description"] = description

        if priority:
            fields["priority"] = {"name": priority}

        if assignee:
            # Try different assignee formats
            if "@" in assignee:
                # Email format
                fields["assignee"] = {"emailAddress": assignee}
            elif assignee.startswith("JIRAUSER") or len(assignee) > 20:
                # Account ID format
                fields["assignee"] = {"accountId": assignee}
            else:
                # Username format
                fields["assignee"] = {"name": assignee}

        if reporter:
            # Try different reporter formats
            if "@" in reporter:
                # Email format
                fields["reporter"] = {"emailAddress": reporter}
            elif reporter.startswith("JIRAUSER") or len(reporter) > 20:
                # Account ID format
                fields["reporter"] = {"accountId": reporter}
            else:
                # Username format
                fields["reporter"] = {"name": reporter}

        if labels:
            fields["labels"] = labels

        if components:
            fields["components"] = [{"name": comp} for comp in components]

        if fix_versions:
            fields["fixVersions"] = [{"name": version} for version in fix_versions]

        # Add custom fields if provided
        if custom_fields:
            fields.update(custom_fields)

        # Create the ticket
        ticket_data = {"fields": fields}
        result = await self._make_request("POST", "issue", data=ticket_data)

        if result["success"]:
            created_ticket = result["data"]
            ticket_key = created_ticket.get("key")

            return {
                "success": True,
                "ticket": {
                    "key": ticket_key,
                    "id": created_ticket.get("id"),
                    "url": f"{self.jira_config.base_url}/browse/{ticket_key}",
                    "self_url": created_ticket.get("self")
                },
                "message": f"Successfully created ticket {ticket_key}",
                "raw_data": created_ticket
            }
        else:
            return result

    @tool_method(name="search_tickets")
    async def search_tickets(
        self,
        jql: str,
        start_at: int = 0,
        max_results: int = 50,
        expand: Optional[str] = None,
        fields: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Search for Jira tickets using JQL (Jira Query Language).

        Args:
            jql: JQL query string (e.g., 'project = PROJ AND status = "In Progress"')
            start_at: Starting index for pagination (default: 0)
            max_results: Maximum number of results to return (default: 50, max: 1000)
            expand: Optional comma-separated list of fields to expand
            fields: Optional comma-separated list of fields to include

        Returns:
            Dictionary with search results or error information
        """
        if not jql:
            return {
                "success": False,
                "error": "JQL query is required"
            }

        # Limit max_results to prevent excessive API calls
        max_results = min(max_results, 1000)

        params = {
            "jql": jql,
            "startAt": start_at,
            "maxResults": max_results
        }

        if expand:
            params["expand"] = expand
        if fields:
            params["fields"] = fields

        result = await self._make_request("GET", "search", params=params)

        if result["success"]:
            search_data = result["data"]
            issues = search_data.get("issues", [])

            # Extract simplified ticket information
            tickets = []
            for issue in issues:
                fields_data = issue.get("fields", {})
                tickets.append({
                    "key": issue.get("key"),
                    "id": issue.get("id"),
                    "summary": fields_data.get("summary"),
                    "status": fields_data.get("status", {}).get("name"),
                    "priority": fields_data.get("priority", {}).get("name"),
                    "assignee": fields_data.get("assignee", {}).get("displayName") if fields_data.get("assignee") else None,
                    "issue_type": fields_data.get("issuetype", {}).get("name"),
                    "project": fields_data.get("project", {}).get("key"),
                    "created": fields_data.get("created"),
                    "updated": fields_data.get("updated"),
                    "url": f"{self.jira_config.base_url}/browse/{issue.get('key')}"
                })

            return {
                "success": True,
                "search_results": {
                    "total": search_data.get("total", 0),
                    "start_at": search_data.get("startAt", 0),
                    "max_results": search_data.get("maxResults", 0),
                    "tickets": tickets
                },
                "raw_data": search_data
            }
        else:
            return result

    @tool_method(name="get_ticket_comments")
    async def get_ticket_comments(
        self,
        ticket_key: str,
        start_at: int = 0,
        max_results: int = 50
    ) -> Dict[str, Any]:
        """
        Get comments for a Jira ticket.

        Args:
            ticket_key: Jira ticket key (e.g., 'PROJ-123') or ID
            start_at: Starting index for pagination (default: 0)
            max_results: Maximum number of results to return (default: 50)

        Returns:
            Dictionary with comments data or error information
        """
        if not ticket_key:
            return {
                "success": False,
                "error": "Ticket key is required"
            }

        params = {
            "startAt": start_at,
            "maxResults": min(max_results, 1000)
        }

        endpoint = f"issue/{quote(ticket_key)}/comment"
        result = await self._make_request("GET", endpoint, params=params)

        if result["success"]:
            comments_data = result["data"]
            comments = comments_data.get("comments", [])

            # Extract simplified comment information
            simplified_comments = []
            for comment in comments:
                author = comment.get("author", {})
                simplified_comments.append({
                    "id": comment.get("id"),
                    "body": comment.get("body"),
                    "author": {
                        "name": author.get("displayName"),
                        "email": author.get("emailAddress"),
                        "account_id": author.get("accountId")
                    },
                    "created": comment.get("created"),
                    "updated": comment.get("updated"),
                    "visibility": comment.get("visibility")
                })

            return {
                "success": True,
                "comments": {
                    "total": comments_data.get("total", 0),
                    "start_at": comments_data.get("startAt", 0),
                    "max_results": comments_data.get("maxResults", 0),
                    "comments": simplified_comments
                },
                "raw_data": comments_data
            }
        else:
            return result

    @tool_method(name="get_ticket_attachments")
    async def get_ticket_attachments(self, ticket_key: str) -> Dict[str, Any]:
        """
        Get attachments for a Jira ticket.

        Args:
            ticket_key: Jira ticket key (e.g., 'PROJ-123') or ID

        Returns:
            Dictionary with attachments data or error information
        """
        if not ticket_key:
            return {
                "success": False,
                "error": "Ticket key is required"
            }

        # Get ticket with attachment field
        params = {"fields": "attachment"}
        endpoint = f"issue/{quote(ticket_key)}"
        result = await self._make_request("GET", endpoint, params=params)

        if result["success"]:
            ticket_data = result["data"]
            attachments = ticket_data.get("fields", {}).get("attachment", [])

            # Extract simplified attachment information
            simplified_attachments = []
            for attachment in attachments:
                author = attachment.get("author", {})
                simplified_attachments.append({
                    "id": attachment.get("id"),
                    "filename": attachment.get("filename"),
                    "size": attachment.get("size"),
                    "mime_type": attachment.get("mimeType"),
                    "content_url": attachment.get("content"),
                    "thumbnail_url": attachment.get("thumbnail"),
                    "author": {
                        "name": author.get("displayName"),
                        "email": author.get("emailAddress"),
                        "account_id": author.get("accountId")
                    },
                    "created": attachment.get("created")
                })

            return {
                "success": True,
                "attachments": simplified_attachments,
                "raw_data": attachments
            }
        else:
            return result

    @tool_method(name="get_project_info")
    async def get_project_info(self, project_key: str) -> Dict[str, Any]:
        """
        Get information about a Jira project including available issue types, priorities, etc.

        Args:
            project_key: Project key (e.g., 'PROJ')

        Returns:
            Dictionary with project information or error details
        """
        if not project_key:
            return {
                "success": False,
                "error": "Project key is required"
            }

        endpoint = f"project/{quote(project_key)}"
        result = await self._make_request("GET", endpoint)

        if result["success"]:
            project_data = result["data"]

            # Extract useful project information
            project_info = {
                "key": project_data.get("key"),
                "id": project_data.get("id"),
                "name": project_data.get("name"),
                "description": project_data.get("description"),
                "lead": project_data.get("lead", {}).get("displayName"),
                "project_type": project_data.get("projectTypeKey"),
                "url": project_data.get("self"),
                "issue_types": [],
                "components": [],
                "versions": []
            }

            # Extract issue types
            for issue_type in project_data.get("issueTypes", []):
                project_info["issue_types"].append({
                    "id": issue_type.get("id"),
                    "name": issue_type.get("name"),
                    "description": issue_type.get("description"),
                    "subtask": issue_type.get("subtask", False)
                })

            # Extract components
            for component in project_data.get("components", []):
                project_info["components"].append({
                    "id": component.get("id"),
                    "name": component.get("name"),
                    "description": component.get("description")
                })

            # Extract versions
            for version in project_data.get("versions", []):
                project_info["versions"].append({
                    "id": version.get("id"),
                    "name": version.get("name"),
                    "description": version.get("description"),
                    "released": version.get("released", False),
                    "archived": version.get("archived", False)
                })

            return {
                "success": True,
                "project": project_info,
                "raw_data": project_data
            }
        else:
            return result

    @tool_method(name="get_create_meta")
    async def get_create_meta(
        self,
        project_key: Optional[str] = None,
        issue_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get metadata for creating issues, including required fields and allowed values.

        Args:
            project_key: Optional project key to filter results
            issue_type: Optional issue type to filter results

        Returns:
            Dictionary with create metadata or error details
        """
        params = {"expand": "projects.issuetypes.fields"}

        if project_key:
            params["projectKeys"] = project_key
        if issue_type:
            params["issuetypeNames"] = issue_type

        result = await self._make_request("GET", "issue/createmeta", params=params)

        if result["success"]:
            meta_data = result["data"]
            projects = meta_data.get("projects", [])

            # Simplify the metadata structure
            simplified_meta = {
                "projects": []
            }

            for project in projects:
                project_info = {
                    "key": project.get("key"),
                    "name": project.get("name"),
                    "issue_types": []
                }

                for issue_type_data in project.get("issuetypes", []):
                    issue_type_info = {
                        "name": issue_type_data.get("name"),
                        "description": issue_type_data.get("description"),
                        "subtask": issue_type_data.get("subtask", False),
                        "required_fields": [],
                        "optional_fields": []
                    }

                    fields = issue_type_data.get("fields", {})
                    for field_key, field_data in fields.items():
                        field_info = {
                            "key": field_key,
                            "name": field_data.get("name"),
                            "type": field_data.get("schema", {}).get("type"),
                            "required": field_data.get("required", False),
                            "allowed_values": []
                        }

                        # Extract allowed values if available
                        allowed_values = field_data.get("allowedValues", [])
                        for value in allowed_values:
                            if isinstance(value, dict):
                                field_info["allowed_values"].append({
                                    "id": value.get("id"),
                                    "name": value.get("name"),
                                    "value": value.get("value")
                                })
                            else:
                                field_info["allowed_values"].append(str(value))

                        if field_info["required"]:
                            issue_type_info["required_fields"].append(field_info)
                        else:
                            issue_type_info["optional_fields"].append(field_info)

                    project_info["issue_types"].append(issue_type_info)

                simplified_meta["projects"].append(project_info)

            return {
                "success": True,
                "create_meta": simplified_meta,
                "raw_data": meta_data
            }
        else:
            return result
