"""
config.py

Configuration management for the Orchestra Template Engine.
Supports loading from YAML files, environment variables, and provides
database URL generation and other application settings.

Author: <PERSON>
"""

from pathlib import Path
from typing import Any, Optional

import yaml
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseConfig(BaseSettings):
    """Database configuration settings."""

    host: str = Field(default="localhost", description="Database host")
    port: int = Field(default=5432, description="Database port")
    name: str = Field(default="orchestra", description="Database name")
    user: str = Field(default="postgres", description="Database user")
    password: str = Field(default="", description="Database password")
    driver: str = Field(default="postgresql", description="Database driver")
    echo: bool = Field(default=False, description="Enable SQL echo logging")
    pool_size: int = Field(default=10, description="Connection pool size")
    max_overflow: int = Field(default=20, description="Max pool overflow")
    pool_timeout: int = Field(default=30, description="Pool timeout in seconds")
    pool_recycle: int = Field(default=3600, description="Pool recycle time in seconds")

    @property
    def url(self) -> str:
        """Generate database URL from configuration."""
        if self.password:
            return f"{self.driver}://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"
        else:
            return f"{self.driver}://{self.user}@{self.host}:{self.port}/{self.name}"

    @property
    def async_url(self) -> str:
        """Generate async database URL from configuration."""
        driver = self.driver.replace("postgresql", "postgresql+asyncpg")
        if self.password:
            return f"{driver}://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"
        else:
            return f"{driver}://{self.user}@{self.host}:{self.port}/{self.name}"

    model_config = SettingsConfigDict(env_prefix="DB_")


class APIConfig(BaseSettings):
    """API configuration settings."""

    title: str = Field(default="Orchestra Template Engine", description="API title")
    description: str = Field(
        default="API for managing templates and rendering them with values",
        description="API description",
    )
    version: str = Field(default="1.0.0", description="API version")
    host: str = Field(default="0.0.0.0", description="API host")
    port: int = Field(default=8000, description="API port")
    debug: bool = Field(default=False, description="Enable debug mode")
    reload: bool = Field(default=False, description="Enable auto-reload")
    workers: int = Field(default=1, description="Number of worker processes")

    @property
    def base_url(self) -> str:
        """Generate base URL for the API."""
        protocol = "https" if self.port == 443 else "http"
        if self.port in [80, 443]:
            return f"{protocol}://{self.host}"
        return f"{protocol}://{self.host}:{self.port}"

    model_config = SettingsConfigDict(env_prefix="API_")


class LoggingConfig(BaseSettings):
    """Logging configuration settings."""

    level: str = Field(default="INFO", description="Logging level")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format",
    )
    file_path: Optional[str] = Field(default=None, description="Log file path")
    max_file_size: int = Field(
        default=10485760, description="Max log file size in bytes (10MB)"
    )
    backup_count: int = Field(default=5, description="Number of backup log files")

    @field_validator("level")
    @classmethod
    def validate_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level. Must be one of: {valid_levels}")
        return v.upper()

    model_config = SettingsConfigDict(env_prefix="LOG_")


class SecurityConfig(BaseSettings):
    """Security configuration settings."""

    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="Secret key for encryption",
    )
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(
        default=30, description="Access token expiration in minutes"
    )
    cors_origins: list[str] = Field(default=["*"], description="CORS allowed origins")
    cors_methods: list[str] = Field(default=["*"], description="CORS allowed methods")
    cors_headers: list[str] = Field(default=["*"], description="CORS allowed headers")

    model_config = SettingsConfigDict(env_prefix="SECURITY_")


class JiraConfig(BaseSettings):
    """Jira integration configuration settings."""

    base_url: str = Field(
        default="",
        description="Jira instance base URL (e.g., https://company.atlassian.net)"
    )
    username: str = Field(
        default="",
        description="Jira username (email for Jira Cloud)"
    )
    api_token: str = Field(
        default="",
        description="Jira API token (recommended) or password"
    )
    auth_type: str = Field(
        default="token",
        description="Authentication type: 'token' or 'basic'"
    )
    timeout: int = Field(
        default=30,
        description="Request timeout in seconds"
    )
    max_retries: int = Field(
        default=3,
        description="Maximum number of retry attempts"
    )
    retry_delay: float = Field(
        default=1.0,
        description="Base delay for exponential backoff in seconds"
    )
    verify_ssl: bool = Field(
        default=True,
        description="Verify SSL certificates"
    )

    model_config = SettingsConfigDict(env_prefix="JIRA_")


class RedisConfig(BaseSettings):
    """Redis configuration settings."""

    host: str = Field(default="localhost", description="Redis host")
    port: int = Field(default=6379, description="Redis port")
    db: int = Field(default=0, description="Redis database number")
    password: Optional[str] = Field(default=None, description="Redis password")
    max_connections: int = Field(default=10, description="Max Redis connections")

    @property
    def url(self) -> str:
        """Generate Redis URL from configuration."""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        else:
            return f"redis://{self.host}:{self.port}/{self.db}"

    model_config = SettingsConfigDict(env_prefix="REDIS_")


def load_yaml_config(config_file: str = "config/settings.yaml") -> dict[str, Any]:
    """
    Load configuration from a YAML file.
    """
    config_path = Path(config_file)
    if not config_path.exists():
        return {}

    try:
        with open(config_path, encoding="utf-8") as f:
            file_content = yaml.safe_load(f)
            return file_content or {}
    except Exception as e:
        print(f"Warning: Could not load YAML config from {config_path}: {e}")
        return {}


class Settings(BaseSettings):
    """Main application settings."""

    # Environment
    environment: str = Field(
        default="development", description="Application environment"
    )

    # Sub-configurations
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    api: APIConfig = Field(default_factory=APIConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    jira: JiraConfig = Field(default_factory=JiraConfig)

    # Application-specific settings
    temp_dir: str = Field(
        default="/tmp/orchestra", description="Temporary directory for operations"
    )
    max_template_size: int = Field(
        default=10485760, description="Max template size in bytes (10MB)"
    )
    template_cache_ttl: int = Field(
        default=3600, description="Template cache TTL in seconds"
    )

    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v):
        valid_envs = ["development", "staging", "production", "testing"]
        if v.lower() not in valid_envs:
            raise ValueError(f"Invalid environment. Must be one of: {valid_envs}")
        return v.lower()

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        yaml_file="config/settings.yaml",
        case_sensitive=False,
        env_nested_delimiter="__",
        extra="ignore",
    )


# Global settings instance
_settings: Optional[Settings] = None


def get_settings(config_file: Optional[str] = None, init_db: bool = False) -> Settings:
    """
    Get application settings. Loads from config file if provided,
    otherwise uses default configuration sources.

    Args:
        config_file: Optional path to YAML configuration file
        init_db: Whether to initialize the database (default: False)

    Returns:
        Settings instance
    """
    global _settings

    if _settings is None:
        # Load YAML config first
        yaml_config = load_yaml_config(config_file or "config/settings.yaml")

        # Create settings with YAML config as defaults
        _settings = Settings(**yaml_config)

    return _settings


def reload_settings(config_file: Optional[str] = None) -> Settings:
    """
    Force reload of settings. Useful for testing or when config changes.

    Args:
        config_file: Optional path to YAML configuration file

    Returns:
        New Settings instance
    """
    global _settings
    _settings = None
    return get_settings(config_file)


# Convenience functions for accessing specific configurations
def get_database_config() -> DatabaseConfig:
    """Get database configuration."""
    return get_settings().database


def get_api_config() -> APIConfig:
    """Get API configuration."""
    return get_settings().api


def get_logging_config() -> LoggingConfig:
    """Get logging configuration."""
    return get_settings().logging


def get_security_config() -> SecurityConfig:
    """Get security configuration."""
    return get_settings().security


def get_redis_config() -> RedisConfig:
    """Get Redis configuration."""
    return get_settings().redis


# URL generation utilities
def get_database_url() -> str:
    """Get database URL."""
    return get_database_config().url


def get_async_database_url() -> str:
    """Get async database URL."""
    return get_database_config().async_url


def get_redis_url() -> str:
    """Get Redis URL."""
    return get_redis_config().url


def get_api_base_url() -> str:
    """Get API base URL."""
    return get_api_config().base_url
