# Jira Tool Documentation

The Jira Tool provides integration with Jira instances (both Cloud and Server) for reading tickets, searching issues, and accessing ticket-related information.

## Overview

The Jira Tool allows you to:
- Read individual Jira tickets by key or ID
- Create new Jira tickets
- Search for tickets using JQL (Jira Query Language)
- Get comments for tickets
- Get attachments for tickets
- Get project information and metadata
- Test connectivity to your Jira instance

## Configuration

### Environment Variables

Configure Jira integration using environment variables:

```bash
# Required Configuration
JIRA__BASE_URL=https://your-company.atlassian.net
JIRA__USERNAME=<EMAIL>
JIRA__API_TOKEN=your-jira-api-token

# Optional Configuration
JIRA__AUTH_TYPE=token  # 'token' or 'basic'
JIRA__TIMEOUT=30
JIRA__MAX_RETRIES=3
JIRA__RETRY_DELAY=1.0
JIRA__VERIFY_SSL=true
```

### Configuration File

Add to `config/settings.yaml`:

```yaml
jira:
  base_url: "https://your-company.atlassian.net"
  username: "<EMAIL>"
  api_token: "your-jira-api-token"
  auth_type: "token"
  timeout: 30
  max_retries: 3
  retry_delay: 1.0
  verify_ssl: true
```

### Authentication

#### API Token (Recommended)
For Jira Cloud, use API tokens:
1. Go to [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
2. Create an API token
3. Use your email as username and the token as the API token

#### Basic Authentication
For Jira Server or if API tokens are not available:
- Set `auth_type` to "basic"
- Use your username and password

## Available Methods

### `jira_tool.test_connection`

Test connectivity to your Jira instance.

**Parameters:** None

**Returns:**
```json
{
  "success": true,
  "message": "Successfully connected to Jira",
  "reachable": true,
  "user": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "account_id": "5b10a2844c20165700ede21g"
  },
  "jira_url": "https://company.atlassian.net"
}
```

### `jira_tool.get_ticket`

Get a Jira ticket by its key or ID.

**Parameters:**
- `ticket_key` (string, required): Jira ticket key (e.g., 'PROJ-123') or ID
- `expand` (string, optional): Comma-separated list of fields to expand

**Example:**
```json
{
  "ticket_key": "PROJ-123",
  "expand": "changelog,renderedFields"
}
```

**Returns:**
```json
{
  "success": true,
  "ticket": {
    "key": "PROJ-123",
    "id": "10001",
    "summary": "Fix login issue",
    "description": "Users cannot log in to the application",
    "status": "In Progress",
    "priority": "High",
    "assignee": "John Doe",
    "reporter": "Jane Smith",
    "created": "2024-01-15T10:30:00.000+0000",
    "updated": "2024-01-16T14:20:00.000+0000",
    "issue_type": "Bug",
    "project": "PROJ",
    "labels": ["urgent", "security"],
    "components": ["Authentication"],
    "fix_versions": ["v1.2.0"],
    "url": "https://company.atlassian.net/browse/PROJ-123"
  },
  "raw_data": { /* Full Jira API response */ }
}
```

### `jira_tool.create_ticket`

Create a new Jira ticket.

**Parameters:**
- `project_key` (string, required): Project key (e.g., 'PROJ')
- `summary` (string, required): Ticket summary/title
- `issue_type` (string, required): Issue type (e.g., 'Bug', 'Task', 'Story')
- `description` (string, optional): Ticket description
- `priority` (string, optional): Priority (e.g., 'High', 'Medium', 'Low')
- `assignee` (string, optional): Assignee username, email, or account ID
- `reporter` (string, optional): Reporter username, email, or account ID
- `labels` (array, optional): List of labels
- `components` (array, optional): List of component names
- `fix_versions` (array, optional): List of fix version names
- `custom_fields` (object, optional): Dictionary of custom field values

**Example:**
```json
{
  "project_key": "PROJ",
  "summary": "Fix login issue",
  "issue_type": "Bug",
  "description": "Users cannot log in to the application",
  "priority": "High",
  "assignee": "<EMAIL>",
  "labels": ["urgent", "security"],
  "components": ["Authentication"]
}
```

**Returns:**
```json
{
  "success": true,
  "ticket": {
    "key": "PROJ-124",
    "id": "10002",
    "url": "https://company.atlassian.net/browse/PROJ-124",
    "self_url": "https://company.atlassian.net/rest/api/2/issue/10002"
  },
  "message": "Successfully created ticket PROJ-124",
  "raw_data": { /* Full Jira API response */ }
}
```

### `jira_tool.search_tickets`

Search for Jira tickets using JQL (Jira Query Language).

**Parameters:**
- `jql` (string, required): JQL query string
- `start_at` (integer, optional): Starting index for pagination (default: 0)
- `max_results` (integer, optional): Maximum results to return (default: 50, max: 1000)
- `expand` (string, optional): Comma-separated list of fields to expand
- `fields` (string, optional): Comma-separated list of fields to include

**Example:**
```json
{
  "jql": "project = PROJ AND status = \"In Progress\"",
  "max_results": 25,
  "fields": "summary,status,assignee"
}
```

**Returns:**
```json
{
  "success": true,
  "search_results": {
    "total": 42,
    "start_at": 0,
    "max_results": 25,
    "tickets": [
      {
        "key": "PROJ-123",
        "id": "10001",
        "summary": "Fix login issue",
        "status": "In Progress",
        "priority": "High",
        "assignee": "John Doe",
        "issue_type": "Bug",
        "project": "PROJ",
        "created": "2024-01-15T10:30:00.000+0000",
        "updated": "2024-01-16T14:20:00.000+0000",
        "url": "https://company.atlassian.net/browse/PROJ-123"
      }
    ]
  },
  "raw_data": { /* Full Jira API response */ }
}
```

### `jira_tool.get_ticket_comments`

Get comments for a Jira ticket.

**Parameters:**
- `ticket_key` (string, required): Jira ticket key or ID
- `start_at` (integer, optional): Starting index for pagination (default: 0)
- `max_results` (integer, optional): Maximum results to return (default: 50)

**Example:**
```json
{
  "ticket_key": "PROJ-123",
  "max_results": 10
}
```

**Returns:**
```json
{
  "success": true,
  "comments": {
    "total": 5,
    "start_at": 0,
    "max_results": 10,
    "comments": [
      {
        "id": "10000",
        "body": "I'm working on this issue now.",
        "author": {
          "name": "John Doe",
          "email": "<EMAIL>",
          "account_id": "5b10a2844c20165700ede21g"
        },
        "created": "2024-01-16T09:15:00.000+0000",
        "updated": "2024-01-16T09:15:00.000+0000",
        "visibility": null
      }
    ]
  },
  "raw_data": { /* Full Jira API response */ }
}
```

### `jira_tool.get_ticket_attachments`

Get attachments for a Jira ticket.

**Parameters:**
- `ticket_key` (string, required): Jira ticket key or ID

**Example:**
```json
{
  "ticket_key": "PROJ-123"
}
```

**Returns:**
```json
{
  "success": true,
  "attachments": [
    {
      "id": "10000",
      "filename": "screenshot.png",
      "size": 245760,
      "mime_type": "image/png",
      "content_url": "https://company.atlassian.net/secure/attachment/10000/screenshot.png",
      "thumbnail_url": "https://company.atlassian.net/secure/thumbnail/10000/_thumb_10000.png",
      "author": {
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "account_id": "5b10a2844c20165700ede21h"
      },
      "created": "2024-01-15T11:00:00.000+0000"
    }
  ],
  "raw_data": [ /* Full Jira API response */ ]
}
```

### `jira_tool.get_project_info`

Get information about a Jira project including available issue types, components, and versions.

**Parameters:**
- `project_key` (string, required): Project key (e.g., 'PROJ')

**Example:**
```json
{
  "project_key": "PROJ"
}
```

**Returns:**
```json
{
  "success": true,
  "project": {
    "key": "PROJ",
    "id": "10000",
    "name": "My Project",
    "description": "Project description",
    "lead": "John Doe",
    "project_type": "software",
    "url": "https://company.atlassian.net/rest/api/2/project/10000",
    "issue_types": [
      {
        "id": "1",
        "name": "Bug",
        "description": "A problem which impairs functionality",
        "subtask": false
      },
      {
        "id": "2",
        "name": "Task",
        "description": "A task that needs to be done",
        "subtask": false
      }
    ],
    "components": [
      {
        "id": "10000",
        "name": "Authentication",
        "description": "User authentication components"
      }
    ],
    "versions": [
      {
        "id": "10000",
        "name": "v1.0.0",
        "description": "First release",
        "released": true,
        "archived": false
      }
    ]
  },
  "raw_data": { /* Full Jira API response */ }
}
```

### `jira_tool.get_create_meta`

Get metadata for creating issues, including required fields and allowed values.

**Parameters:**
- `project_key` (string, optional): Project key to filter results
- `issue_type` (string, optional): Issue type to filter results

**Example:**
```json
{
  "project_key": "PROJ",
  "issue_type": "Bug"
}
```

**Returns:**
```json
{
  "success": true,
  "create_meta": {
    "projects": [
      {
        "key": "PROJ",
        "name": "My Project",
        "issue_types": [
          {
            "name": "Bug",
            "description": "A problem which impairs functionality",
            "subtask": false,
            "required_fields": [
              {
                "key": "summary",
                "name": "Summary",
                "type": "string",
                "required": true,
                "allowed_values": []
              },
              {
                "key": "issuetype",
                "name": "Issue Type",
                "type": "issuetype",
                "required": true,
                "allowed_values": [
                  {
                    "id": "1",
                    "name": "Bug",
                    "value": "Bug"
                  }
                ]
              }
            ],
            "optional_fields": [
              {
                "key": "priority",
                "name": "Priority",
                "type": "priority",
                "required": false,
                "allowed_values": [
                  {
                    "id": "1",
                    "name": "High",
                    "value": "High"
                  },
                  {
                    "id": "2",
                    "name": "Medium",
                    "value": "Medium"
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  "raw_data": { /* Full Jira API response */ }
}
```

## JQL Examples

Here are some common JQL queries you can use with `search_tickets`:

```jql
# Find all open tickets assigned to you
assignee = currentUser() AND status != Done

# Find high priority bugs in a specific project
project = PROJ AND issuetype = Bug AND priority = High

# Find tickets updated in the last week
updated >= -1w

# Find tickets with specific labels
labels in (urgent, security)

# Find tickets in specific status
status in ("In Progress", "Code Review")

# Complex query with multiple conditions
project = PROJ AND status != Done AND assignee = currentUser() AND priority in (High, Critical)
```

## Error Handling

All methods return a consistent error format:

```json
{
  "success": false,
  "error": "Error description",
  "status_code": 401
}
```

Common error scenarios:
- **401 Unauthorized**: Invalid credentials
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Ticket or resource doesn't exist
- **Network errors**: Connection timeouts or network issues

## Security Considerations

1. **API Tokens**: Use API tokens instead of passwords when possible
2. **Environment Variables**: Store credentials in environment variables, not in code
3. **SSL Verification**: Keep `verify_ssl` enabled in production
4. **Permissions**: Ensure the Jira user has appropriate read permissions

## Troubleshooting

### Connection Issues
1. Verify the base URL is correct
2. Check if the Jira instance is accessible
3. Ensure credentials are valid
4. Test with `jira_tool.test_connection`

### Authentication Issues
1. For Jira Cloud, use email and API token
2. For Jira Server, use username and password
3. Check if the account has necessary permissions

### Permission Issues
1. Ensure the user can access the project
2. Check if the user can view the specific ticket
3. Verify read permissions for comments and attachments
