# API Call Tool Documentation

The API Call Tool provides secure HTTP client functionality for making requests to external APIs and services. It includes comprehensive safety constraints, automatic retry logic with exponential backoff, response handling, and support for all major HTTP methods.

## Overview

The `ApiCallTool` class offers six main capabilities:
1. **GET Requests**: Retrieve data from APIs with query parameters and retry logic
2. **POST Requests**: Send data to APIs (form data or JSON) with retry support
3. **PUT Requests**: Update resources with data and automatic retries
4. **DELETE Requests**: Remove resources with retry functionality
5. **HEAD Requests**: Get metadata without response body, with retries
6. **Connectivity Testing**: Test API availability with intelligent retry logic

## Retry Functionality

All HTTP methods include built-in retry logic with:
- **Exponential Backoff**: Delays increase exponentially between retries (1s, 2s, 4s, etc.)
- **Jitter**: Random variation (±25%) added to delays to prevent thundering herd
- **Smart Retry Logic**: Only retries on appropriate errors and status codes
- **Configurable**: Customizable retry count and base delay
- **Retry Information**: Detailed retry statistics in responses

## Available Methods

### 1. `api_call_tool.get`

Make a GET request to retrieve data from an API endpoint with automatic retry logic.

**Parameters:**
- `url` (string, required): Target URL for the request
- `headers` (object, optional): Custom HTTP headers
- `params` (object, optional): Query parameters to append to URL
- `timeout` (integer, optional): Request timeout in seconds (default: 30)
- `max_retries` (integer, optional): Maximum number of retry attempts (default: 3)
- `retry_delay` (float, optional): Base delay for exponential backoff in seconds (default: 1.0)

**Example Usage:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "api_call_tool.get",
    "arguments": {
      "url": "https://api.github.com/repos/octocat/Hello-World",
      "headers": {
        "Accept": "application/vnd.github.v3+json",
        "Authorization": "token YOUR_TOKEN"
      },
      "params": {
        "per_page": "10",
        "page": "1"
      },
      "max_retries": 5,
      "retry_delay": 2.0
    }
  }
}
```

**Response:**
```json
{
  "status_code": 200,
  "status_text": "OK",
  "headers": {
    "content-type": "application/json; charset=utf-8",
    "server": "GitHub.com"
  },
  "url": "https://api.github.com/repos/octocat/Hello-World?per_page=10&page=1",
  "elapsed_ms": 245,
  "success": true,
  "body": {
    "id": 1296269,
    "name": "Hello-World",
    "full_name": "octocat/Hello-World"
  },
  "body_type": "json",
  "body_size": 1024,
  "retry_info": {
    "attempts": 2,
    "max_retries": 5,
    "succeeded_on_retry": true
  }
}
```

### 2. `api_call_tool.post`

Make a POST request to send data to an API endpoint.

**Parameters:**
- `url` (string, required): Target URL for the request
- `data` (object, optional): Form data (will be form-encoded)
- `json_data` (object, optional): JSON data (will be JSON-encoded)
- `headers` (object, optional): Custom HTTP headers
- `timeout` (integer, optional): Request timeout in seconds (default: 30)

**Note:** You can provide either `data` OR `json_data`, but not both.

**Example Usage (JSON):**
```json
{
  "method": "tools/call",
  "params": {
    "name": "api_call_tool.post",
    "arguments": {
      "url": "https://api.example.com/users",
      "json_data": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "developer"
      },
      "headers": {
        "Authorization": "Bearer YOUR_TOKEN"
      }
    }
  }
}
```

**Example Usage (Form Data):**
```json
{
  "method": "tools/call",
  "params": {
    "name": "api_call_tool.post",
    "arguments": {
      "url": "https://api.example.com/contact",
      "data": {
        "name": "John Doe",
        "message": "Hello from Orchestra!"
      }
    }
  }
}
```

### 3. `api_call_tool.put`

Make a PUT request to update a resource.

**Parameters:**
- `url` (string, required): Target URL for the request
- `data` (object, optional): Form data (will be form-encoded)
- `json_data` (object, optional): JSON data (will be JSON-encoded)
- `headers` (object, optional): Custom HTTP headers
- `timeout` (integer, optional): Request timeout in seconds (default: 30)

**Example Usage:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "api_call_tool.put",
    "arguments": {
      "url": "https://api.example.com/users/123",
      "json_data": {
        "name": "John Smith",
        "email": "<EMAIL>"
      }
    }
  }
}
```

### 4. `api_call_tool.delete`

Make a DELETE request to remove a resource.

**Parameters:**
- `url` (string, required): Target URL for the request
- `headers` (object, optional): Custom HTTP headers
- `timeout` (integer, optional): Request timeout in seconds (default: 30)

**Example Usage:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "api_call_tool.delete",
    "arguments": {
      "url": "https://api.example.com/users/123",
      "headers": {
        "Authorization": "Bearer YOUR_TOKEN"
      }
    }
  }
}
```

### 5. `api_call_tool.head`

Make a HEAD request to get response metadata without the body.

**Parameters:**
- `url` (string, required): Target URL for the request
- `headers` (object, optional): Custom HTTP headers
- `timeout` (integer, optional): Request timeout in seconds (default: 30)

**Example Usage:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "api_call_tool.head",
    "arguments": {
      "url": "https://api.example.com/large-file.json"
    }
  }
}
```

**Response (no body):**
```json
{
  "status_code": 200,
  "status_text": "OK",
  "headers": {
    "content-type": "application/json",
    "content-length": "1048576",
    "last-modified": "Wed, 01 Jun 2024 12:00:00 GMT"
  },
  "url": "https://api.example.com/large-file.json",
  "elapsed_ms": 89,
  "success": true
}
```

### 6. `api_call_tool.test_connectivity`

Test connectivity to a URL with minimal overhead.

**Parameters:**
- `url` (string, required): Target URL to test
- `timeout` (integer, optional): Request timeout in seconds (default: 10)

**Example Usage:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "api_call_tool.test_connectivity",
    "arguments": {
      "url": "https://api.example.com/health"
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "reachable": true,
  "status_code": 200,
  "elapsed_ms": 156,
  "final_url": "https://api.example.com/health",
  "server": "nginx/1.18.0"
}
```

## Retry Behavior

### When Retries Occur

The API call tool automatically retries requests in the following scenarios:

**Network/Connection Errors:**
- `TimeoutException` - Request timeout
- `ConnectError` - Connection failed
- `ReadError` - Error reading response

**HTTP Status Codes:**
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error
- `502` - Bad Gateway
- `503` - Service Unavailable
- `504` - Gateway Timeout
- `507` - Insufficient Storage
- `508` - Loop Detected
- `510` - Not Extended
- `511` - Network Authentication Required

### When Retries Don't Occur

Retries are **NOT** attempted for:
- Client errors (4xx except 429): 400, 401, 403, 404, etc.
- URL validation errors
- Invalid parameters
- Authentication failures

### Exponential Backoff

Retry delays follow exponential backoff with jitter:
- **Attempt 1**: `base_delay * 1` ± 25% jitter
- **Attempt 2**: `base_delay * 2` ± 25% jitter
- **Attempt 3**: `base_delay * 4` ± 25% jitter
- **Maximum delay**: Capped at 60 seconds

**Example with `retry_delay=1.0`:**
- Retry 1: ~1.0s (0.75-1.25s range)
- Retry 2: ~2.0s (1.5-2.5s range)
- Retry 3: ~4.0s (3.0-5.0s range)

### Retry Information

All responses include retry information when retries were attempted:

```json
{
  "retry_info": {
    "attempts": 3,
    "max_retries": 3,
    "succeeded_on_retry": true
  }
}
```

- `attempts`: Total number of attempts made
- `max_retries`: Maximum retries configured
- `succeeded_on_retry`: Whether the request eventually succeeded

### Configuring Retries

**Default Settings:**
- `max_retries`: 3 attempts
- `retry_delay`: 1.0 second base delay
- `test_connectivity`: 2 retries, 0.5s delay (faster for health checks)

**Custom Configuration:**
```json
{
  "name": "api_call_tool.get",
  "arguments": {
    "url": "https://api.unreliable-service.com/data",
    "max_retries": 5,
    "retry_delay": 2.0
  }
}
```

**Disable Retries:**
```json
{
  "name": "api_call_tool.get",
  "arguments": {
    "url": "https://api.service.com/data",
    "max_retries": 0
  }
}
```

## Security Features

### URL Validation

The tool implements several security measures:

1. **Protocol Restriction**: Only HTTP and HTTPS protocols are allowed
2. **Hostname Validation**: URLs must have valid hostnames
3. **Private Network Blocking**: Blocks access to:
   - `localhost`, `127.0.0.1`, `0.0.0.0`
   - IPv6 localhost (`::1`)
   - Link-local addresses (`169.254.*`)
   - Private IP ranges (`10.*`, `172.16.*`, `192.168.*`)

### Example Security Blocks

```json
// Blocked: localhost access
{
  "success": false,
  "error": "Access to localhost is not allowed for security reasons",
  "status_code": 0
}

// Blocked: invalid protocol
{
  "success": false,
  "error": "URL must use HTTP or HTTPS protocol",
  "status_code": 0
}
```

## Response Format

All API call methods return a consistent response format:

### Successful Response
```json
{
  "status_code": 200,
  "status_text": "OK",
  "headers": {
    "content-type": "application/json",
    "server": "nginx/1.18.0"
  },
  "url": "https://api.example.com/endpoint",
  "elapsed_ms": 234,
  "success": true,
  "body": "Response content (JSON object or string)",
  "body_type": "json|text",
  "body_size": 1024
}
```

### Error Response
```json
{
  "success": false,
  "error": "Request failed: Connection timeout",
  "status_code": 0
}
```

## Common Use Cases

### 1. API Health Checks
```json
{
  "name": "api_call_tool.test_connectivity",
  "arguments": {
    "url": "https://api.service.com/health",
    "timeout": 5
  }
}
```

### 2. REST API Operations
```json
// Create resource
{
  "name": "api_call_tool.post",
  "arguments": {
    "url": "https://api.service.com/items",
    "json_data": {"name": "New Item", "category": "tools"}
  }
}

// Read resource
{
  "name": "api_call_tool.get",
  "arguments": {
    "url": "https://api.service.com/items/123"
  }
}

// Update resource
{
  "name": "api_call_tool.put",
  "arguments": {
    "url": "https://api.service.com/items/123",
    "json_data": {"name": "Updated Item"}
  }
}

// Delete resource
{
  "name": "api_call_tool.delete",
  "arguments": {
    "url": "https://api.service.com/items/123"
  }
}
```

### 3. Authentication Examples

**Bearer Token:**
```json
{
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**API Key:**
```json
{
  "headers": {
    "X-API-Key": "your-api-key-here"
  }
}
```

**Basic Auth (Base64 encoded):**
```json
{
  "headers": {
    "Authorization": "Basic dXNlcm5hbWU6cGFzc3dvcmQ="
  }
}
```

## Error Handling

The tool handles various error scenarios:

### Timeout Errors
```json
{
  "success": false,
  "error": "Request timed out after 30 seconds",
  "status_code": 0
}
```

### Network Errors
```json
{
  "success": false,
  "error": "Request failed: Name or service not known",
  "status_code": 0
}
```

### HTTP Errors
```json
{
  "status_code": 404,
  "status_text": "Not Found",
  "success": false,
  "body": "Resource not found",
  "body_type": "text"
}
```

## Best Practices

### 1. Use Appropriate HTTP Methods
- **GET**: Retrieve data (idempotent)
- **POST**: Create new resources
- **PUT**: Update existing resources (idempotent)
- **DELETE**: Remove resources (idempotent)
- **HEAD**: Check resource existence/metadata

### 2. Handle Timeouts
- Use shorter timeouts for health checks (5-10 seconds)
- Use longer timeouts for data processing APIs (30-60 seconds)
- Consider the API's expected response time

### 3. Authentication
- Always use HTTPS for authenticated requests
- Store API keys securely
- Use appropriate authentication methods (Bearer tokens, API keys)

### 4. Error Handling
- Check the `success` field before processing responses
- Handle different HTTP status codes appropriately
- Implement retry logic for transient failures

### 5. Rate Limiting
- Be mindful of API rate limits
- Implement delays between requests if needed
- Monitor response headers for rate limit information

## Integration Examples

### With Workflow Tool
```python
# Get API data and create workflow
api_data = await api_call_tool.get("https://api.service.com/config")
if api_data["success"]:
    config = api_data["body"]
    await workflow_tool.create_workflow(
        name=f"Process {config['name']}",
        description=f"Workflow for {config['description']}"
    )
```

### With Script Tool
```python
# Test API connectivity before running scripts
connectivity = await api_call_tool.test_connectivity("https://api.service.com")
if connectivity["reachable"]:
    await script_tool.run_predefined_script("api_integration_script")
```

## Troubleshooting

### Common Issues

1. **SSL Certificate Errors**: Ensure the target API has valid SSL certificates
2. **CORS Issues**: CORS only affects browser requests, not server-side API calls
3. **Rate Limiting**: Check API documentation for rate limits and implement delays
4. **Authentication**: Verify API keys and tokens are valid and have proper permissions

### Debug Tips

1. **Use HEAD requests** to test connectivity without downloading large responses
2. **Check response headers** for debugging information
3. **Monitor elapsed_ms** to identify slow APIs
4. **Test with curl** first to verify API behavior outside the tool
