#!/usr/bin/env python3
"""
create_ticket_example.py

Simple example demonstrating how to create a Jira ticket using the Jira Tool.

Author: <PERSON>
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.mcp_server.tools.jira_tool import JiraTool


async def main():
    """Demonstrate creating a Jira ticket."""
    print("🎫 Jira Ticket Creation Example")
    print("=" * 40)
    
    # Initialize the tool
    tool = JiraTool()
    
    # Test connection first
    print("1. Testing Jira connection...")
    connection_result = await tool.test_connection()
    
    if not connection_result.get("success"):
        print("❌ Cannot connect to Jira. Please configure your Jira settings:")
        print("   - Set JIRA__BASE_URL environment variable")
        print("   - Set JIRA__USERNAME environment variable")
        print("   - Set JIRA__API_TOKEN environment variable")
        return
    
    print("✅ Connected to <PERSON><PERSON> successfully!")
    print()
    
    # Example project and ticket data
    project_key = "PROJ"  # Replace with your project key
    
    print("2. Getting project information...")
    project_result = await tool.get_project_info(project_key)
    
    if project_result.get("success"):
        project = project_result["project"]
        print(f"   ✅ Project: {project['name']} ({project['key']})")
        
        # Get available issue types
        issue_types = [it['name'] for it in project['issue_types']]
        print(f"   🎯 Available issue types: {issue_types}")
        
        # Use the first available issue type
        issue_type = issue_types[0] if issue_types else "Task"
    else:
        print(f"   ❌ Could not get project info: {project_result.get('error')}")
        print("   Using default issue type 'Task'")
        issue_type = "Task"
    
    print()
    
    # Create a new ticket
    print("3. Creating a new ticket...")
    
    ticket_data = {
        "project_key": project_key,
        "summary": "Example ticket created via API",
        "issue_type": issue_type,
        "description": """This is an example ticket created using the Jira Tool.

**Purpose:** Demonstrate ticket creation functionality

**Details:**
- Created via Python script
- Uses Jira REST API
- Includes proper error handling

This ticket can be safely deleted after testing.""",
        "priority": "Medium",
        "labels": ["api-created", "example", "automation"],
        "components": []  # Add component names if your project has them
    }
    
    create_result = await tool.create_ticket(**ticket_data)
    
    if create_result.get("success"):
        ticket = create_result["ticket"]
        print(f"   ✅ Successfully created ticket: {ticket['key']}")
        print(f"   🔗 URL: {ticket['url']}")
        print(f"   📝 Message: {create_result['message']}")
        
        # Get the created ticket to verify
        print()
        print("4. Verifying created ticket...")
        verify_result = await tool.get_ticket(ticket['key'])
        
        if verify_result.get("success"):
            verified_ticket = verify_result["ticket"]
            print(f"   ✅ Verified ticket: {verified_ticket['key']}")
            print(f"   📝 Summary: {verified_ticket['summary']}")
            print(f"   📊 Status: {verified_ticket['status']}")
            print(f"   🏷️  Labels: {verified_ticket['labels']}")
        else:
            print(f"   ❌ Could not verify ticket: {verify_result.get('error')}")
            
    else:
        print(f"   ❌ Failed to create ticket: {create_result.get('error')}")
        print()
        print("Common reasons for failure:")
        print("   - Project key doesn't exist")
        print("   - Issue type not available in project")
        print("   - Insufficient permissions")
        print("   - Required fields missing")
        print()
        print("Try getting create metadata first:")
        print(f"   await tool.get_create_meta('{project_key}')")
    
    print()
    print("🎉 Ticket creation example completed!")


if __name__ == "__main__":
    # Check if basic configuration is available
    if not os.getenv("JIRA__BASE_URL"):
        print("⚠️  No Jira configuration found.")
        print("   Set the following environment variables to test ticket creation:")
        print("   - JIRA__BASE_URL")
        print("   - JIRA__USERNAME") 
        print("   - JIRA__API_TOKEN")
        print()
    
    asyncio.run(main())
